# 词云配置功能说明

## 概述

本功能为浏览器扩展添加了完整的词云样式配置系统，允许用户自定义词云的显示样式和行为。

## 功能特性

### 1. 字体设置
- **字体族选择**：支持多种字体族（serif、sans-serif、monospace等）
- **字体样式**：正常、斜体、倾斜
- **字体粗细**：正常、粗体、较细、较粗
- **字体大小范围**：可设置最小和最大字体大小（8px-100px）
- **文字旋转**：
  - 启用/禁用旋转
  - 随机旋转或固定角度序列
  - 可自定义旋转角度

### 2. 颜色方案
- **预设颜色方案**：
  - Category 10/20
  - Pastel 1/2
  - Set 1/2/3
  - Tableau 10
- **自定义颜色**：
  - 支持添加/删除自定义颜色
  - 颜色选择器和十六进制输入
  - 预设颜色快速选择

### 3. 布局设置
- **布局算法**：阿基米德螺旋或矩形螺旋
- **词语间距**：0-20px可调
- **布局密度**：10%-100%可调
- **尺寸设置**：
  - 宽度：200-1200px
  - 高度：150-800px

### 4. 动画效果
- **启用/禁用动画**
- **动画持续时间**：200-3000ms可调

### 5. 高级设置
- **随机种子**：控制布局的一致性
- **配置导入/导出**：保存和分享配置
- **一键重置**：恢复到默认设置

## 使用方法

### 访问配置页面
1. 右键点击扩展图标
2. 选择"选项"或"设置"
3. 在设置页面中选择"词云配置"标签

### 实时预览
- 配置页面包含实时预览功能
- 任何配置更改都会立即在预览中显示
- 可以通过"显示/隐藏预览"按钮控制预览显示

### 配置保存
- 所有配置更改会自动保存
- 使用浏览器扩展的持久化存储
- 配置在不同页面和会话间保持同步

### 重置配置
1. 点击"重置默认"按钮
2. 在确认对话框中点击"确认重置"
3. 所有配置将恢复到默认值

## 技术实现

### 架构组件
- **Redux状态管理**：`wordCloudConfigSlice.ts`
- **配置组件**：`WordCloudSettings.tsx`
- **词云组件**：更新的`WordCloud.tsx`
- **颜色工具**：`colorSchemes.ts`
- **UI组件**：基于shadcn/ui的组件库

### 配置数据结构
```typescript
interface WordCloudConfig {
  // 字体设置
  fontFamily: FontFamily
  fontStyle: "normal" | "italic" | "oblique"
  fontWeight: "normal" | "bold" | "lighter" | "bolder" | number
  
  // 字体大小
  minFontSize: number
  maxFontSize: number
  
  // 旋转设置
  rotationEnabled: boolean
  rotationAngles: number[]
  randomRotation: boolean
  
  // 颜色方案
  colorScheme: ColorScheme
  customColors: string[]
  
  // 布局设置
  spiralType: SpiralType
  padding: number
  layoutDensity: number
  
  // 动画设置
  animationEnabled: boolean
  animationDuration: number
  
  // 尺寸设置
  width: number
  height: number
  
  // 其他设置
  randomSeed: number
}
```

### 集成方式
1. 配置数据通过Redux store管理
2. WordCloud组件自动使用当前配置
3. 配置更改实时应用到所有词云实例
4. 使用浏览器扩展的storage API持久化

## 扩展性

### 添加新配置项
1. 在`wordCloudConfigSlice.ts`中添加新的配置字段
2. 在`WordCloudSettings.tsx`中添加对应的UI控件
3. 在`WordCloud.tsx`中使用新的配置项

### 添加新颜色方案
1. 在`colorSchemes.ts`中添加新的颜色数组
2. 在`wordCloudConfigSlice.ts`中更新`COLOR_SCHEMES`枚举
3. 在配置组件中添加新的选项

## 注意事项

1. **性能考虑**：大尺寸词云可能影响性能
2. **浏览器兼容性**：依赖现代浏览器的CSS和JavaScript特性
3. **存储限制**：配置数据存储在浏览器扩展的同步存储中
4. **颜色方案**：自定义颜色数量建议不超过20个以保证性能

## 故障排除

### 配置不生效
- 检查浏览器控制台是否有错误
- 尝试重置配置到默认值
- 重新加载扩展

### 预览不显示
- 确保预览功能已启用
- 检查词云组件是否正确加载
- 验证配置数据是否有效

### 颜色显示异常
- 检查自定义颜色格式是否正确（#RRGGBB）
- 尝试切换到预设颜色方案
- 清除自定义颜色并重新添加
