import * as React from "react"
import { But<PERSON> } from "./button"
import { Input } from "./input"
import { Label } from "./label"
import { cn } from "../../lib/utils"

interface ColorPickerProps {
  value: string
  onChange: (color: string) => void
  label?: string
  className?: string
}

const ColorPicker = React.forwardRef<HTMLDivElement, ColorPickerProps>(
  ({ value, onChange, label, className }, ref) => {
    const [isOpen, setIsOpen] = React.useState(false)
    const [inputValue, setInputValue] = React.useState(value)

    React.useEffect(() => {
      setInputValue(value)
    }, [value])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value
      setInputValue(newValue)
      if (newValue.match(/^#[0-9A-Fa-f]{6}$/)) {
        onChange(newValue)
      }
    }

    const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newColor = e.target.value
      setInputValue(newColor)
      onChange(newColor)
    }

    const presetColors = [
      "#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd",
      "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf",
      "#aec7e8", "#ffbb78", "#98df8a", "#ff9896", "#c5b0d5",
      "#c49c94", "#f7b6d3", "#c7c7c7", "#dbdb8d", "#9edae5"
    ]

    return (
      <div ref={ref} className={cn("space-y-2", className)}>
        {label && <Label>{label}</Label>}
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Button
              type="button"
              variant="outline"
              className="w-12 h-10 p-0 border-2"
              style={{ backgroundColor: value }}
              onClick={() => setIsOpen(!isOpen)}
            >
              <span className="sr-only">选择颜色</span>
            </Button>
            {isOpen && (
              <div className="absolute top-12 left-0 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-64">
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm">颜色选择器</Label>
                    <input
                      type="color"
                      value={value}
                      onChange={handleColorChange}
                      className="w-full h-10 border border-gray-300 rounded cursor-pointer"
                    />
                  </div>
                  <div>
                    <Label className="text-sm">十六进制值</Label>
                    <Input
                      value={inputValue}
                      onChange={handleInputChange}
                      placeholder="#000000"
                      className="font-mono"
                    />
                  </div>
                  <div>
                    <Label className="text-sm mb-2 block">预设颜色</Label>
                    <div className="grid grid-cols-10 gap-1">
                      {presetColors.map((color) => (
                        <button
                          key={color}
                          type="button"
                          className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                          style={{ backgroundColor: color }}
                          onClick={() => {
                            setInputValue(color)
                            onChange(color)
                          }}
                          title={color}
                        />
                      ))}
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setIsOpen(false)}
                    >
                      完成
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
          <Input
            value={inputValue}
            onChange={handleInputChange}
            placeholder="#000000"
            className="font-mono flex-1"
          />
        </div>
      </div>
    )
  }
)

ColorPicker.displayName = "ColorPicker"

export { ColorPicker }
